using System.Collections.Generic;
using System;
using Random = UnityEngine.Random;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Events;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.ResourceManagement.ResourceProviders;
using UnityEngine.SceneManagement;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Represents the result of an addressable operation, including success/failure status and exception information.
    /// </summary>
    public class AddressableOperationResult
    {
        /// <summary>
        /// Gets a value indicating whether the operation completed successfully.
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// Gets the exception that occurred during the operation, or null if the operation was successful.
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="AddressableOperationResult"/> class for a successful operation.
        /// </summary>
        public AddressableOperationResult()
        {
            IsSuccess = true;
            Exception = null;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AddressableOperationResult"/> class for a failed operation.
        /// </summary>
        /// <param name="exception">The exception that occurred during the operation.</param>
        public AddressableOperationResult(Exception exception)
        {
            IsSuccess = false;
            Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        }
    }

    /// <summary>
    /// An instantiable helper class to manage the lifecycle of Addressable assets.
    /// Each instance of this class tracks the assets it has loaded and provides
    /// a single point of cleanup for releasing them.
    /// </summary>
    public class AddressablesHelper
    {
        private readonly Dictionary<object, AsyncOperationHandle> assetHandles = new();
        private readonly Dictionary<GameObject, AsyncOperationHandle<GameObject>> instanceHandles = new();

        #region Asset Loading
        /// <summary>
        /// Asynchronously loads a single asset by its key (address, label, or AssetReference).
        /// Tracks the handle for future release. Returns the loaded asset or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of asset to load.</typeparam>
        /// <param name="key">Address, label, or AssetReference key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>The loaded asset, or null if loading fails.</returns>
        public async Awaitable<TObject> LoadAssetAsync<TObject>(object key, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            if (assetHandles.TryGetValue(key, out var existingHandle))
            {
                return await existingHandle.Convert<TObject>().Task;
            }

            var handle = Addressables.LoadAssetAsync<TObject>(key);
            assetHandles[key] = handle;

            var result = await handle.Task;
            if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to load asset with key: {key}. Reason: {handle.OperationException}");
                assetHandles.Remove(key);
                Addressables.Release(handle);
                onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
                return null;
            }
            onComplete?.Invoke(new AddressableOperationResult());
            return result;
        }

        /// <summary>
        /// Asynchronously loads multiple assets that match the given key (typically a label).
        /// Tracks the handle for future release. Returns the loaded assets or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of assets to load.</typeparam>
        /// <param name="key">Label or address key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>List of loaded assets, or null if loading fails.</returns>
        public async Awaitable<IList<TObject>> LoadAssetsAsync<TObject>(object key, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            if (assetHandles.TryGetValue(key, out var existingHandle))
            {
                return await existingHandle.Convert<IList<TObject>>().Task;
            }

            var handle = Addressables.LoadAssetsAsync<TObject>(key, null);
            assetHandles[key] = handle;

            var result = await handle.Task;
            if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to load assets with key: {key}. Reason: {handle.OperationException}");
                assetHandles.Remove(key);
                Addressables.Release(handle);
                onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
                return null;
            }
            onComplete?.Invoke(new AddressableOperationResult());
            return result;
        }

        /// <summary>
        /// Asynchronously loads a random asset from a collection identified by a key.
        /// The loaded asset is tracked by its address for individual release and cleanup.
        /// Invokes the provided callback with the key object (address) of the loaded asset.
        /// Returns the loaded asset or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of asset to load.</typeparam>
        /// <param name="key">Label or address key.</param>
        /// <param name="onKeySelected">Callback invoked with the selected asset key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>The loaded random asset, or null if loading fails.</returns>
        public async Awaitable<TObject> LoadRandomAssetAsync<TObject>(object key, UnityAction<object> onKeySelected = null, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            var locationsHandle = Addressables.LoadResourceLocationsAsync(key);
            var locations = await locationsHandle.Task;

            if (locations == null || locations.Count == 0)
            {
                Debug.LogError($"[AddressablesHelper] Found no assets for random selection with key: {key}");
                Addressables.Release(locationsHandle);
                onComplete?.Invoke(new AddressableOperationResult(new Exception($"No assets found for key: {key}")));
                return null;
            }

            var randomLocation = locations[Random.Range(0, locations.Count)];
            Addressables.Release(locationsHandle);

            var assetKey = randomLocation.PrimaryKey;

            onKeySelected?.Invoke(assetKey);

            return await LoadAssetAsync<TObject>(assetKey, onComplete);
        }

        /// <summary>
        /// Loads a single asset by its key (address, label, or AssetReference) using a callback.
        /// Tracks the handle for future release. Invokes the callback with the loaded asset or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of asset to load.</typeparam>
        /// <param name="key">Address, label, or AssetReference key.</param>
        /// <param name="onResult">Callback invoked with the loaded asset (or null if failed).</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void LoadAsset<TObject>(object key, UnityAction<TObject> onResult, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            try
            {
                var result = await LoadAssetAsync<TObject>(key, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in LoadAsset callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Loads multiple assets that match the given key (typically a label) using a callback.
        /// Tracks the handle for future release. Invokes the callback with the loaded assets or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of assets to load.</typeparam>
        /// <param name="key">Label or address key.</param>
        /// <param name="onResult">Callback invoked with the loaded assets (or null if failed).</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void LoadAssets<TObject>(object key, UnityAction<IList<TObject>> onResult, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            try
            {
                var result = await LoadAssetsAsync<TObject>(key, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in LoadAssets callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Loads a random asset from a collection identified by a key using a callback.
        /// The loaded asset is tracked by its address for individual release and cleanup.
        /// Invokes the provided callback with the key object (address) of the loaded asset.
        /// Invokes the result callback with the loaded asset or null if loading fails.
        /// </summary>
        /// <typeparam name="TObject">Type of asset to load.</typeparam>
        /// <param name="key">Label or address key.</param>
        /// <param name="onResult">Callback invoked with the loaded random asset (or null if failed).</param>
        /// <param name="onKeySelected">Optional callback invoked with the selected asset key.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void LoadRandomAsset<TObject>(object key, UnityAction<TObject> onResult, UnityAction<object> onKeySelected = null, UnityAction<AddressableOperationResult> onComplete = null) where TObject : class
        {
            try
            {
                var result = await LoadRandomAssetAsync<TObject>(key, onKeySelected, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in LoadRandomAsset callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }
        #endregion

        #region Instantiation

        /// <summary>
        /// Asynchronously instantiates a GameObject from an addressable asset with no parent (world‐space).
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>The instantiated GameObject, or null if instantiation fails.</returns>
        public async Awaitable<GameObject> InstantiateAsync(object key, UnityAction<AddressableOperationResult> onComplete = null)
        {
            return await InstantiateAsync(key, parent: null, instantiateInWorldSpace: false, onComplete: onComplete);
        }

        /// <summary>
        /// Asynchronously instantiates a GameObject from an addressable asset at a specific position and rotation.
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="position">World‐space position for the instantiated GameObject.</param>
        /// <param name="rotation">World‐space rotation for the instantiated GameObject.</param>
        /// <param name="parent">Optional parent Transform for the instantiated GameObject.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>The instantiated GameObject, or null if instantiation fails.</returns>
        public async Awaitable<GameObject> InstantiateAsync(object key, Vector3 position, Quaternion rotation, Transform parent = null, UnityAction<AddressableOperationResult> onComplete = null)
        {
            var handle = Addressables.InstantiateAsync(key, position, rotation, parent);
            var result = await handle.Task;

            if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to instantiate asset at position/rotation with key: {key}. Reason: {handle.OperationException}");
                Addressables.ReleaseInstance(handle);
                onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
                return null;
            }
            instanceHandles[result] = handle;
            onComplete?.Invoke(new AddressableOperationResult());
            return result;
        }

        /// <summary>
        /// Asynchronously instantiates a GameObject from an addressable asset.
        /// Each instantiated object is tracked individually for later release.
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="parent">The parent Transform for the instantiated GameObject (or null).</param>
        /// <param name="instantiateInWorldSpace">If true, preserves the prefab’s world‐space transform; otherwise, it’s relative to parent.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>The instantiated GameObject, or null if instantiation fails.</returns>
        public async Awaitable<GameObject> InstantiateAsync(object key, Transform parent = null, bool instantiateInWorldSpace = false, UnityAction<AddressableOperationResult> onComplete = null)
        {
            var handle = Addressables.InstantiateAsync(key, parent, instantiateInWorldSpace);
            var result = await handle.Task;

            if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to instantiate asset with key: {key}. Reason: {handle.OperationException}");
                Addressables.ReleaseInstance(handle);
                onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
                return null;
            }
            instanceHandles[result] = handle;
            onComplete?.Invoke(new AddressableOperationResult());
            return result;
        }

        /// <summary>
        /// Instantiates a GameObject from an addressable asset with no parent (world‐space) using a callback.
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="onResult">Callback invoked with the instantiated GameObject (or null if failed).</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void Instantiate(object key, UnityAction<GameObject> onResult, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await InstantiateAsync(key, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in Instantiate callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Instantiates a GameObject from an addressable asset at a specific position and rotation using a callback.
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="position">World‐space position for the instantiated GameObject.</param>
        /// <param name="rotation">World‐space rotation for the instantiated GameObject.</param>
        /// <param name="onResult">Callback invoked with the instantiated GameObject (or null if failed).</param>
        /// <param name="parent">Optional parent Transform for the instantiated GameObject.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void Instantiate(object key, Vector3 position, Quaternion rotation, UnityAction<GameObject> onResult, Transform parent = null, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await InstantiateAsync(key, position, rotation, parent, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in Instantiate callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Instantiates a GameObject from an addressable asset using a callback.
        /// Each instantiated object is tracked individually for later release.
        /// </summary>
        /// <param name="key">Address, label, or AssetReference of the prefab.</param>
        /// <param name="onResult">Callback invoked with the instantiated GameObject (or null if failed).</param>
        /// <param name="parent">The parent Transform for the instantiated GameObject (or null).</param>
        /// <param name="instantiateInWorldSpace">If true, preserves the prefab's world‐space transform; otherwise, it's relative to parent.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void Instantiate(object key, UnityAction<GameObject> onResult, Transform parent = null, bool instantiateInWorldSpace = false, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await InstantiateAsync(key, parent, instantiateInWorldSpace, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in Instantiate callback: {ex}");
                onResult?.Invoke(null);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }
        #endregion

        #region Scene Loading
        /// <summary>
        /// Asynchronously loads a scene by its key. The scene is not activated automatically.
        /// The handle is tracked for future unloading.
        /// </summary>
        /// <param name="sceneKey">Address, label, or AssetReference key.</param>
        /// <param name="loadMode">Defines whether the scene should be loaded in Single or Additive mode.</param>
        /// <param name="activateOnLoad">If true, the scene is activated immediately after loading.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>An AsyncOperationHandle for the loaded scene instance, which can be used for activation.</returns>
        public async Awaitable<SceneInstance> LoadSceneAsync(object sceneKey, LoadSceneMode loadMode = LoadSceneMode.Additive, bool activateOnLoad = false, UnityAction<AddressableOperationResult> onComplete = null)
        {
            if (assetHandles.ContainsKey(sceneKey))
            {
                Debug.LogWarning($"[AddressablesHelper] Scene with key '{sceneKey}' is already loaded or being loaded by this helper. Returning existing handle.");
                return assetHandles[sceneKey].Convert<SceneInstance>().Result;
            }

            var handle = Addressables.LoadSceneAsync(sceneKey, loadMode, activateOnLoad);
            assetHandles[sceneKey] = handle;

            var result = await handle.Task;

            if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to load scene with key: {sceneKey}. Reason: {handle.OperationException}");
                Addressables.Release(handle);
                assetHandles.Remove(sceneKey);
                onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
            }
            else
            {
                onComplete?.Invoke(new AddressableOperationResult());
            }
            return result;
        }

        /// <summary>
        /// Loads a scene by its key using a callback. The scene is not activated automatically.
        /// The handle is tracked for future unloading.
        /// </summary>
        /// <param name="sceneKey">Address, label, or AssetReference key.</param>
        /// <param name="onResult">Callback invoked with the loaded scene instance (or default if failed).</param>
        /// <param name="loadMode">Defines whether the scene should be loaded in Single or Additive mode.</param>
        /// <param name="activateOnLoad">If true, the scene is activated immediately after loading.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void LoadScene(object sceneKey, UnityAction<SceneInstance> onResult, LoadSceneMode loadMode = LoadSceneMode.Additive, bool activateOnLoad = false, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await LoadSceneAsync(sceneKey, loadMode, activateOnLoad, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in LoadScene callback: {ex}");
                onResult?.Invoke(default);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Asynchronously unloads a scene that was previously loaded with this helper.
        /// This is an alias for ReleaseAsset for clarity when working with scenes.
        /// </summary>
        /// <param name="sceneKey">Address, label, or AssetReference key.</param>
        /// <param name="autoReleaseHandle">If true, the handle will be automatically released after unloading.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        public async Awaitable UnloadSceneAsync(object sceneKey, bool autoReleaseHandle = true, UnityAction<AddressableOperationResult> onComplete = null)
        {
            if (assetHandles.TryGetValue(sceneKey, out var existingHandle))
            {
                assetHandles.Remove(sceneKey);
                var handle = Addressables.UnloadSceneAsync(existingHandle, autoReleaseHandle);
                await handle.Task;
                if (handle.IsValid() && handle.Status != AsyncOperationStatus.Succeeded)
                {
                    Debug.LogError($"[AddressablesHelper] Failed to unload scene with key: {sceneKey}.");
                    Addressables.Release(handle);
                    onComplete?.Invoke(new AddressableOperationResult(handle.OperationException));
                }
                else
                {
                    onComplete?.Invoke(new AddressableOperationResult());
                }
            }
            else
            {
                Debug.LogWarning($"[AddressablesHelper] Tried to unload a scene with key '{sceneKey}' that was not loaded or already released by this helper.");
                onComplete?.Invoke(new AddressableOperationResult(new Exception($"Scene with key '{sceneKey}' was not loaded or already released.")));
            }
        }

        /// <summary>
        /// Unloads a scene that was previously loaded with this helper using a callback.
        /// This is an alias for ReleaseAsset for clarity when working with scenes.
        /// </summary>
        /// <param name="sceneKey">Address, label, or AssetReference key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <param name="autoReleaseHandle">If true, the handle will be automatically released after unloading.</param>
        public async void UnloadScene(object sceneKey, UnityAction<AddressableOperationResult> onComplete, bool autoReleaseHandle = true)
        {
            try
            {
                await UnloadSceneAsync(sceneKey, autoReleaseHandle, onComplete);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in UnloadScene callback: {ex}");
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }
        #endregion

        #region Catalog Management
        /// <summary>
        /// Checks if a content catalog update is available.
        /// </summary>
        /// <returns>A list of catalog identifiers that have updates, or an empty list if none.</returns>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        public async Awaitable<List<string>> CheckForCatalogUpdatesAsync(UnityAction<AddressableOperationResult> onComplete = null)
        {
            var checkHandle = Addressables.CheckForCatalogUpdates(false);
            var catalogsToUpdate = await checkHandle.Task;

            if (checkHandle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to check for catalog updates. Reason: {checkHandle.OperationException}");
                catalogsToUpdate = new List<string>();
                onComplete?.Invoke(new AddressableOperationResult(checkHandle.OperationException));
            }
            else
            {
                onComplete?.Invoke(new AddressableOperationResult());
            }

            Addressables.Release(checkHandle);
            return catalogsToUpdate;
        }

        /// <summary>
        /// Updates the content catalogs.
        /// </summary>
        /// <param name="catalogsToUpdate">The specific list of catalog identifiers to update. If set to null, all available catalogs will be updated.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>True if the update was successful, false otherwise.</returns>
        public async Awaitable<bool> UpdateCatalogsAsync(List<string> catalogsToUpdate = null, UnityAction<AddressableOperationResult> onComplete = null)
        {
            var updateHandle = Addressables.UpdateCatalogs(catalogsToUpdate, false);
            await updateHandle.Task;

            bool success = updateHandle.Status == AsyncOperationStatus.Succeeded;

            if (!success)
            {
                Debug.LogError($"[AddressablesHelper] Failed to update catalogs. Reason: {updateHandle.OperationException}");
                onComplete?.Invoke(new AddressableOperationResult(updateHandle.OperationException));
            }
            else
            {
                onComplete?.Invoke(new AddressableOperationResult());
            }

            Addressables.Release(updateHandle);
            return success;
        }

        /// <summary>
        /// Checks if a content catalog update is available using a callback.
        /// </summary>
        /// <param name="onResult">Callback invoked with a list of catalog identifiers that have updates, or an empty list if none.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void CheckForCatalogUpdates(UnityAction<List<string>> onResult, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await CheckForCatalogUpdatesAsync(onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in CheckForCatalogUpdates callback: {ex}");
                onResult?.Invoke(new List<string>());
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Updates the content catalogs using a callback.
        /// </summary>
        /// <param name="onResult">Callback invoked with true if the update was successful, false otherwise.</param>
        /// <param name="catalogsToUpdate">The specific list of catalog identifiers to update. If set to null, all available catalogs will be updated.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void UpdateCatalogs(UnityAction<bool> onResult, List<string> catalogsToUpdate = null, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await UpdateCatalogsAsync(catalogsToUpdate, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in UpdateCatalogs callback: {ex}");
                onResult?.Invoke(false);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }
        #endregion

        #region Download & Preload
        /// <summary>
        /// Gets the download size for the assets corresponding to the given key.
        /// </summary>
        /// <param name="key">Label or address key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <returns>Download size in bytes.</returns>
        public async Awaitable<long> GetDownloadSizeAsync(object key, UnityAction<AddressableOperationResult> onComplete = null)
        {
            var sizeHandle = Addressables.GetDownloadSizeAsync(key);
            var size = await sizeHandle.Task;
            if (sizeHandle.IsValid() && sizeHandle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to get download size for key: {key}. Reason: {sizeHandle.OperationException}");
                size = 0;
                onComplete?.Invoke(new AddressableOperationResult(sizeHandle.OperationException));
            }
            else
            {
                onComplete?.Invoke(new AddressableOperationResult());
            }
            Addressables.Release(sizeHandle);
            return size;
        }

        /// <summary>
        /// Pre-downloads the dependencies for the assets corresponding to the given key.
        /// </summary>
        /// <param name="key">Label or address key.</param>
        /// <param name="progressCallback">Callback for download progress (0-1).</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        public async Awaitable PreloadDependenciesAsync(object key, UnityAction<float> progressCallback = null, UnityAction<AddressableOperationResult> onComplete = null)
        {
            var downloadHandle = Addressables.DownloadDependenciesAsync(key);
            while (!downloadHandle.IsDone)
            {
                progressCallback?.Invoke(downloadHandle.PercentComplete);
                await Awaitable.NextFrameAsync();
            }
            progressCallback?.Invoke(1f);

            if (downloadHandle.IsValid() && downloadHandle.Status != AsyncOperationStatus.Succeeded)
            {
                Debug.LogError($"[AddressablesHelper] Failed to download dependencies for key: {key}");
                onComplete?.Invoke(new AddressableOperationResult(downloadHandle.OperationException));
            }
            else
            {
                onComplete?.Invoke(new AddressableOperationResult());
            }
            Addressables.Release(downloadHandle);
        }

        /// <summary>
        /// Gets the download size for the assets corresponding to the given key using a callback.
        /// </summary>
        /// <param name="key">Label or address key.</param>
        /// <param name="onResult">Callback invoked with the download size in bytes.</param>
        /// <param name="onComplete">Optional callback invoked when the operation completes, providing operation result details.</param>
        public async void GetDownloadSize(object key, UnityAction<long> onResult, UnityAction<AddressableOperationResult> onComplete = null)
        {
            try
            {
                var result = await GetDownloadSizeAsync(key, onComplete);
                onResult?.Invoke(result);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in GetDownloadSize callback: {ex}");
                onResult?.Invoke(0);
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }

        /// <summary>
        /// Pre-downloads the dependencies for the assets corresponding to the given key using a callback.
        /// </summary>
        /// <param name="key">Label or address key.</param>
        /// <param name="onComplete">Callback invoked when the operation completes, providing operation result details.</param>
        /// <param name="progressCallback">Optional callback for download progress (0-1).</param>
        public async void PreloadDependencies(object key, UnityAction<AddressableOperationResult> onComplete, UnityAction<float> progressCallback = null)
        {
            try
            {
                await PreloadDependenciesAsync(key, progressCallback, onComplete);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AddressablesHelper] Exception in PreloadDependencies callback: {ex}");
                onComplete?.Invoke(new AddressableOperationResult(ex));
            }
        }
        #endregion

        #region Release
        /// <summary>
        /// Releases a loaded asset by the key it was loaded with.
        /// Works for all assets, including those loaded randomly.
        /// </summary>
        /// <param name="key">Key used to load the asset.</param>
        public void ReleaseAsset(object key)
        {
            if (assetHandles.TryGetValue(key, out var handle))
            {
                assetHandles.Remove(key);
                Addressables.Release(handle);
            }
            else
            {
                Debug.LogWarning($"[AddressablesHelper] Tried to release an asset with key '{key}' that was not loaded or already released by this helper.");
            }
        }

        /// <summary>
        /// Releases and destroys a GameObject that was previously instantiated by this helper.
        /// </summary>
        /// <param name="instance">The GameObject instance to release.</param>
        public void ReleaseInstance(GameObject instance)
        {
            if (instanceHandles.TryGetValue(instance, out var handle))
            {
                instanceHandles.Remove(instance);
                Addressables.ReleaseInstance(handle);
            }
            else
            {
                Debug.LogWarning($"[AddressablesHelper] Tried to release an instance of '{instance.name}' that was not tracked or already released by this helper.");
            }
        }

        /// <summary>
        /// Releases all resources that have been tracked by this helper instance.
        /// This includes loaded assets and instantiated GameObjects.
        /// </summary>
        public void ReleaseAllAssets()
        {
            foreach (var handle in assetHandles.Values)
            {
                Addressables.Release(handle);
            }
            assetHandles.Clear();

            foreach (var handle in instanceHandles.Values)
            {
                Addressables.ReleaseInstance(handle);
            }
            instanceHandles.Clear();
        }
        #endregion
    }
}