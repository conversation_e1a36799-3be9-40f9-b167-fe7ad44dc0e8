using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.ResourceManagement.ResourceProviders;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Test script to verify that the callback-based AddressablesHelper methods work correctly.
    /// This script demonstrates how to use the new callback-based API.
    /// </summary>
    public class AddressablesHelperTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private string testAssetKey = "TestAsset";
        [SerializeField] private string testPrefabKey = "TestPrefab";
        [SerializeField] private string testSceneKey = "TestScene";
        [SerializeField] private string testLabelKey = "TestLabel";

        private AddressablesHelper addressablesHelper;

        private void Start()
        {
            addressablesHelper = new AddressablesHelper();

            // Test all callback-based methods
            TestCallbackMethods();
        }

        private void TestCallbackMethods()
        {
            Debug.Log("[AddressablesHelperTest] Starting callback-based method tests...");

            // Test LoadAsset callback
            addressablesHelper.LoadAsset<Texture2D>(testAssetKey,
                onResult: (texture) =>
                {
                    if (texture != null)
                        Debug.Log($"[Test] LoadAsset callback: Successfully loaded texture '{texture.name}'");
                    else
                        Debug.Log("[Test] LoadAsset callback: Failed to load texture");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] LoadAsset operation completed. Success: {result.IsSuccess}");
                });

            // Test LoadAssets callback
            addressablesHelper.LoadAssets<GameObject>(testLabelKey,
                onResult: (gameObjects) =>
                {
                    if (gameObjects != null)
                        Debug.Log($"[Test] LoadAssets callback: Successfully loaded {gameObjects.Count} GameObjects");
                    else
                        Debug.Log("[Test] LoadAssets callback: Failed to load GameObjects");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] LoadAssets operation completed. Success: {result.IsSuccess}");
                });

            // Test LoadRandomAsset callback
            addressablesHelper.LoadRandomAsset<GameObject>(testLabelKey,
                onResult: (randomObject) =>
                {
                    if (randomObject != null)
                        Debug.Log($"[Test] LoadRandomAsset callback: Successfully loaded random GameObject '{randomObject.name}'");
                    else
                        Debug.Log("[Test] LoadRandomAsset callback: Failed to load random GameObject");
                },
                onKeySelected: (selectedKey) =>
                {
                    Debug.Log($"[Test] LoadRandomAsset: Selected key '{selectedKey}'");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] LoadRandomAsset operation completed. Success: {result.IsSuccess}");
                });

            // Test Instantiate callback
            addressablesHelper.Instantiate(testPrefabKey,
                onResult: (instance) =>
                {
                    if (instance != null)
                    {
                        Debug.Log($"[Test] Instantiate callback: Successfully instantiated '{instance.name}'");
                        // Position the instance for visibility
                        instance.transform.position = Vector3.zero;
                    }
                    else
                        Debug.Log("[Test] Instantiate callback: Failed to instantiate prefab");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] Instantiate operation completed. Success: {result.IsSuccess}");
                });

            // Test LoadScene callback
            addressablesHelper.LoadScene(testSceneKey,
                onResult: (sceneInstance) =>
                {
                    if (sceneInstance.Scene.isLoaded)
                        Debug.Log($"[Test] LoadScene callback: Successfully loaded scene '{sceneInstance.Scene.name}'");
                    else
                        Debug.Log("[Test] LoadScene callback: Failed to load scene");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] LoadScene operation completed. Success: {result.IsSuccess}");
                });

            // Test CheckForCatalogUpdates callback
            addressablesHelper.CheckForCatalogUpdates(
                onResult: (catalogsToUpdate) =>
                {
                    Debug.Log($"[Test] CheckForCatalogUpdates callback: Found {catalogsToUpdate.Count} catalogs to update");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] CheckForCatalogUpdates operation completed. Success: {result.IsSuccess}");
                });

            // Test UpdateCatalogs callback
            addressablesHelper.UpdateCatalogs(
                onResult: (success) =>
                {
                    Debug.Log($"[Test] UpdateCatalogs callback: Update success: {success}");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] UpdateCatalogs operation completed. Success: {result.IsSuccess}");
                });

            // Test GetDownloadSize callback
            addressablesHelper.GetDownloadSize(testLabelKey,
                onResult: (size) =>
                {
                    Debug.Log($"[Test] GetDownloadSize callback: Download size: {size} bytes");
                },
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] GetDownloadSize operation completed. Success: {result.IsSuccess}");
                });

            // Test PreloadDependencies callback
            addressablesHelper.PreloadDependencies(testLabelKey,
                onComplete: (result) =>
                {
                    Debug.Log($"[Test] PreloadDependencies operation completed. Success: {result.IsSuccess}");
                },
                progressCallback: (progress) =>
                {
                    Debug.Log($"[Test] PreloadDependencies progress: {progress:P}");
                });
        }

        private void OnDestroy()
        {
            // Clean up all loaded assets
            addressablesHelper?.ReleaseAllAssets();
        }
    }
}
