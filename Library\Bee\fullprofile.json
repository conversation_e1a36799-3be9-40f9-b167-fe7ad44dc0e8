{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 9748, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 9748, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 9748, "tid": 17, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 9748, "tid": 17, "ts": 1754372756359877, "dur": 1731, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756367453, "dur": 1250, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 9748, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 9748, "tid": 1, "ts": 1754372755134902, "dur": 41150, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 9748, "tid": 1, "ts": 1754372755176056, "dur": 105967, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 9748, "tid": 1, "ts": 1754372755282041, "dur": 120330, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756368709, "dur": 1726, "ph": "X", "name": "", "args": {}}, {"pid": 9748, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755130632, "dur": 23608, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755154243, "dur": 1187352, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755155670, "dur": 3544, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755159223, "dur": 784, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755160014, "dur": 6953, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755166978, "dur": 230, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755167218, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755167332, "dur": 1011, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755168351, "dur": 21630, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755189988, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755189996, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755190053, "dur": 1019, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191080, "dur": 276, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191361, "dur": 10, "ph": "X", "name": "ProcessMessages 13121", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191373, "dur": 47, "ph": "X", "name": "ReadAsync 13121", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191423, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191427, "dur": 47, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191476, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191478, "dur": 38, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191519, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191521, "dur": 42, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191565, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191567, "dur": 33, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191603, "dur": 38, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191644, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191646, "dur": 44, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191692, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191694, "dur": 38, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191734, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191736, "dur": 38, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191775, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191777, "dur": 33, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191814, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191847, "dur": 34, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191883, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191886, "dur": 39, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191926, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191929, "dur": 34, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191965, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755191967, "dur": 39, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192008, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192009, "dur": 37, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192048, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192050, "dur": 38, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192091, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192093, "dur": 43, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192138, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192140, "dur": 39, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192181, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192183, "dur": 45, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192230, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192232, "dur": 46, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192279, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192281, "dur": 36, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192320, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192323, "dur": 41, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192365, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192368, "dur": 39, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192410, "dur": 3, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192414, "dur": 38, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192454, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192456, "dur": 43, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192501, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192503, "dur": 35, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192540, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192541, "dur": 33, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192576, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192578, "dur": 39, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192620, "dur": 36, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192658, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192660, "dur": 36, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192699, "dur": 33, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192734, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192736, "dur": 37, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192775, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192777, "dur": 37, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192816, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192818, "dur": 31, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192852, "dur": 38, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192892, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192894, "dur": 35, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192932, "dur": 43, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192977, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755192980, "dur": 61, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193042, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193044, "dur": 41, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193087, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193089, "dur": 40, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193132, "dur": 34, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193169, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193171, "dur": 36, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193209, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193210, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193247, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193249, "dur": 28, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193280, "dur": 35, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193319, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193354, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193355, "dur": 34, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193392, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193394, "dur": 36, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193431, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193433, "dur": 35, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193471, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193473, "dur": 32, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193508, "dur": 29, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193541, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193588, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193589, "dur": 70, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193663, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193665, "dur": 38, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193706, "dur": 29, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193738, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193741, "dur": 125, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193871, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193929, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193932, "dur": 56, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193992, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755193994, "dur": 63, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194061, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194063, "dur": 57, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194124, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194128, "dur": 53, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194185, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194239, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194241, "dur": 63, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194309, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194312, "dur": 71, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194389, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194392, "dur": 89, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194488, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194542, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194543, "dur": 58, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194604, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194607, "dur": 47, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194657, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194659, "dur": 59, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194720, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194721, "dur": 54, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194778, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194780, "dur": 37, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194819, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194821, "dur": 58, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194885, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194953, "dur": 2, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755194956, "dur": 52, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195012, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195016, "dur": 44, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195064, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195067, "dur": 38, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195109, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195169, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195172, "dur": 34, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195208, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195211, "dur": 40, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195255, "dur": 69, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195330, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195333, "dur": 60, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195398, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195401, "dur": 46, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195450, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195453, "dur": 39, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195496, "dur": 32, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195531, "dur": 43, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195578, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195581, "dur": 40, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195622, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195624, "dur": 43, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195669, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195672, "dur": 28, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195703, "dur": 56, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195764, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195802, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195833, "dur": 43, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195878, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195880, "dur": 35, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195916, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195918, "dur": 31, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195951, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195953, "dur": 37, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195992, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755195994, "dur": 31, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196028, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196066, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196099, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196101, "dur": 41, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196145, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196184, "dur": 32, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196220, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196255, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196294, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196296, "dur": 37, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196336, "dur": 37, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196377, "dur": 30, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196409, "dur": 28, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196439, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196443, "dur": 40, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196486, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196487, "dur": 35, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196524, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196525, "dur": 32, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196559, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196560, "dur": 35, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196598, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196638, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196639, "dur": 44, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196687, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196727, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196753, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196782, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196814, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196816, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196850, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196851, "dur": 40, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196894, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196896, "dur": 38, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196936, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196938, "dur": 35, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755196976, "dur": 31, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197010, "dur": 32, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197045, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197077, "dur": 44, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197124, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197159, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197161, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197194, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197195, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197231, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197233, "dur": 33, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197269, "dur": 35, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197306, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197308, "dur": 33, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197343, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197346, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197390, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197392, "dur": 42, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197436, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197438, "dur": 38, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197480, "dur": 43, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197526, "dur": 43, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197571, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197573, "dur": 35, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197611, "dur": 31, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197645, "dur": 31, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197679, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197716, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197717, "dur": 65, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197785, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197787, "dur": 63, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197852, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197854, "dur": 32, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197888, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197890, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197924, "dur": 34, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197960, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197962, "dur": 34, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197998, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755197999, "dur": 35, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198036, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198038, "dur": 30, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198072, "dur": 31, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198106, "dur": 37, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198145, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198147, "dur": 32, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198181, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198183, "dur": 32, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198217, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198219, "dur": 39, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198262, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198306, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198308, "dur": 37, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198349, "dur": 40, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198391, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198392, "dur": 30, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198426, "dur": 31, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198460, "dur": 40, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198502, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198504, "dur": 35, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198542, "dur": 60, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198604, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198605, "dur": 30, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198639, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198676, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198677, "dur": 31, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198712, "dur": 41, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198755, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198756, "dur": 38, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198798, "dur": 28, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755198830, "dur": 185, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199018, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199060, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199062, "dur": 37, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199101, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199103, "dur": 36, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199142, "dur": 30, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199176, "dur": 30, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199209, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199248, "dur": 30, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199281, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199318, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199319, "dur": 35, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199356, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199357, "dur": 29, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199390, "dur": 46, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199439, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199481, "dur": 33, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199518, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199521, "dur": 38, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199563, "dur": 59, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199625, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199628, "dur": 31, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199662, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199695, "dur": 32, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199729, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199731, "dur": 33, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199767, "dur": 35, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199805, "dur": 35, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199843, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199844, "dur": 30, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199876, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199878, "dur": 34, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199915, "dur": 40, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199957, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755199960, "dur": 40, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200002, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200004, "dur": 32, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200040, "dur": 28, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200070, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200104, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200106, "dur": 29, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200138, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200171, "dur": 31, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200204, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200206, "dur": 40, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200250, "dur": 37, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200289, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200291, "dur": 30, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200323, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200325, "dur": 38, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200366, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200397, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200428, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200460, "dur": 32, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200495, "dur": 28, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200526, "dur": 35, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200563, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200565, "dur": 32, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200600, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200627, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200628, "dur": 61, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200693, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200697, "dur": 45, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200751, "dur": 2, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200755, "dur": 36, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200794, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200797, "dur": 55, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200856, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200861, "dur": 45, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200908, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200909, "dur": 32, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200944, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200945, "dur": 35, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755200984, "dur": 28, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201015, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201047, "dur": 67, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201117, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201119, "dur": 39, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201160, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201162, "dur": 33, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201196, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201198, "dur": 27, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201228, "dur": 27, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201258, "dur": 27, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201287, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201289, "dur": 50, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201343, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201380, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201382, "dur": 28, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201411, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201413, "dur": 33, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201448, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201450, "dur": 36, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201489, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201518, "dur": 41, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201561, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201563, "dur": 47, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201613, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201615, "dur": 36, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201654, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201656, "dur": 41, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201700, "dur": 37, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201739, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201741, "dur": 127, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201872, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201913, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201914, "dur": 36, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201952, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201954, "dur": 34, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755201991, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202022, "dur": 50, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202075, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202081, "dur": 142, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202227, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202294, "dur": 2, "ph": "X", "name": "ProcessMessages 1690", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202297, "dur": 39, "ph": "X", "name": "ReadAsync 1690", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202340, "dur": 57, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202400, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202406, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202474, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202478, "dur": 57, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202538, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202541, "dur": 50, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202595, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202600, "dur": 44, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202647, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202649, "dur": 50, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202702, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202705, "dur": 29, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202736, "dur": 69, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202808, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202810, "dur": 40, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202852, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202855, "dur": 32, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202891, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202931, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202933, "dur": 39, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202974, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755202976, "dur": 61, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203040, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203042, "dur": 37, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203081, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203082, "dur": 34, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203121, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203161, "dur": 32, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203195, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203197, "dur": 30, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203230, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203231, "dur": 34, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203267, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203269, "dur": 32, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203303, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203305, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203336, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203338, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203376, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203378, "dur": 30, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203410, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203412, "dur": 45, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203460, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203462, "dur": 36, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203557, "dur": 61, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203620, "dur": 2, "ph": "X", "name": "ProcessMessages 2238", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203624, "dur": 36, "ph": "X", "name": "ReadAsync 2238", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203662, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203664, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203709, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203711, "dur": 34, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203747, "dur": 3, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203751, "dur": 34, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203787, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203789, "dur": 51, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203844, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203883, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203885, "dur": 38, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203926, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203928, "dur": 35, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755203977, "dur": 31, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755204010, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755204012, "dur": 31, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755204046, "dur": 3594, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755207754, "dur": 60, "ph": "X", "name": "ProcessMessages 20494", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755207816, "dur": 353, "ph": "X", "name": "ReadAsync 20494", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755208222, "dur": 1147, "ph": "X", "name": "ProcessMessages 4231", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209375, "dur": 189, "ph": "X", "name": "ReadAsync 4231", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209568, "dur": 9, "ph": "X", "name": "ProcessMessages 12302", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209578, "dur": 43, "ph": "X", "name": "ReadAsync 12302", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209623, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209626, "dur": 28, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209658, "dur": 76, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209737, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209769, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209809, "dur": 49, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209861, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209863, "dur": 31, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209898, "dur": 94, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755209994, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210029, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210031, "dur": 32, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210066, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210068, "dur": 32, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210105, "dur": 63, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210171, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210173, "dur": 33, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210208, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210210, "dur": 46, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210260, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210295, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210372, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210408, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210410, "dur": 45, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210459, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210462, "dur": 35, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210500, "dur": 103, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210608, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210678, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210682, "dur": 68, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210754, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210757, "dur": 63, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210825, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755210828, "dur": 226, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755211059, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755211062, "dur": 1214, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212285, "dur": 3, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212290, "dur": 185, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212478, "dur": 14, "ph": "X", "name": "ProcessMessages 8895", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212494, "dur": 47, "ph": "X", "name": "ReadAsync 8895", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212544, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212547, "dur": 55, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212604, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212606, "dur": 36, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212645, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212648, "dur": 36, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212685, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212687, "dur": 29, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212720, "dur": 28, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212752, "dur": 101, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212857, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212891, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212894, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212938, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755212941, "dur": 78, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213023, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213068, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213071, "dur": 47, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213120, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213123, "dur": 35, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213160, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213162, "dur": 42, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213205, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213208, "dur": 36, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213246, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213248, "dur": 35, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213285, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213287, "dur": 28, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213319, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213404, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213437, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213440, "dur": 38, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213481, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213483, "dur": 36, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213573, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213577, "dur": 46, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213624, "dur": 1, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755213654, "dur": 37, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214755, "dur": 3, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214759, "dur": 143, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214905, "dur": 8, "ph": "X", "name": "ProcessMessages 9566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214914, "dur": 38, "ph": "X", "name": "ReadAsync 9566", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214954, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214956, "dur": 39, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214997, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755214999, "dur": 31, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215032, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215033, "dur": 34, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215071, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215106, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215108, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215185, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215229, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215231, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215268, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215270, "dur": 93, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215368, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215411, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215414, "dur": 32, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215448, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215451, "dur": 35, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215489, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215491, "dur": 40, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215533, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215536, "dur": 32, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215570, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215573, "dur": 36, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215611, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215613, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215646, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215729, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215766, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215768, "dur": 33, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215804, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215806, "dur": 76, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215885, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215931, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215933, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755215968, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216012, "dur": 47, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216064, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216109, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216111, "dur": 34, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216147, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755216149, "dur": 1129, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217285, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217288, "dur": 152, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217443, "dur": 13, "ph": "X", "name": "ProcessMessages 8759", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217458, "dur": 40, "ph": "X", "name": "ReadAsync 8759", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217500, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217502, "dur": 35, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217539, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217541, "dur": 83, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217628, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217662, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217665, "dur": 62, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217729, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217732, "dur": 82, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217817, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217850, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217852, "dur": 33, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217887, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217889, "dur": 80, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755217973, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218013, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218015, "dur": 33, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218052, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218053, "dur": 33, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218090, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218161, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218195, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218197, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218228, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218229, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218266, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218269, "dur": 36, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218307, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218310, "dur": 40, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218351, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218353, "dur": 26, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218384, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218414, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218416, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218493, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218538, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218540, "dur": 46, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218589, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218591, "dur": 37, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218630, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218632, "dur": 26, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218660, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218662, "dur": 31, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218695, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218698, "dur": 31, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218732, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218734, "dur": 88, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218825, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218874, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218885, "dur": 39, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218927, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755218930, "dur": 69, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755219001, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755219003, "dur": 36, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755219042, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755219045, "dur": 25, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755219075, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220331, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220337, "dur": 169, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220509, "dur": 11, "ph": "X", "name": "ProcessMessages 9450", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220521, "dur": 45, "ph": "X", "name": "ReadAsync 9450", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220568, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220570, "dur": 31, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220603, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220605, "dur": 91, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220703, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220763, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220766, "dur": 57, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220826, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220828, "dur": 48, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220879, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220882, "dur": 66, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220952, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220987, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755220989, "dur": 82, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221075, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221110, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221112, "dur": 38, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221151, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221153, "dur": 30, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221185, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221187, "dur": 84, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221275, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221315, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221317, "dur": 63, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221382, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221384, "dur": 34, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221420, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221422, "dur": 34, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221459, "dur": 30, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221492, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221494, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221531, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221568, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221601, "dur": 121, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221727, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755221760, "dur": 798, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755222566, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755222570, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755222629, "dur": 477, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755223112, "dur": 1591, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224709, "dur": 22, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224734, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224794, "dur": 3, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224798, "dur": 75, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224880, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224930, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755224934, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225037, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225039, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225085, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225207, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225254, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225258, "dur": 77, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225342, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225384, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225387, "dur": 106, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225500, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225554, "dur": 5, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225562, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225624, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225626, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225681, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225684, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225714, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225768, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225770, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225798, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225800, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225911, "dur": 51, "ph": "X", "name": "ReadAsync 15", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755225965, "dur": 395, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226367, "dur": 74, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226446, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226450, "dur": 74, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226552, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226593, "dur": 23, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226619, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226851, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226912, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226915, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755226973, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227029, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227032, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227073, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227075, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227151, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227290, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227357, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227361, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227422, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227427, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227608, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227645, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227648, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227813, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227819, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227954, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755227958, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228045, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228047, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228094, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228098, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228234, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228296, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228299, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228356, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228394, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228397, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228465, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228467, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228511, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228513, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228642, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228645, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755228698, "dur": 2053, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230760, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230882, "dur": 13, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230898, "dur": 43, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230947, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755230996, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231000, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231035, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231038, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231136, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231208, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231211, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231281, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231285, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231333, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231393, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231436, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231482, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231517, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231520, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231588, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231645, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231649, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231787, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231790, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231845, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231848, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231951, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755231955, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232028, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232045, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232125, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232173, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232177, "dur": 84, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232264, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232267, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232377, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232381, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232445, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232449, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232486, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232489, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232545, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232639, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232642, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232783, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232785, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232815, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232818, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232904, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232963, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755232965, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233026, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233089, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233092, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233142, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233144, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233250, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233299, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233302, "dur": 96, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233403, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233407, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233454, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233457, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233595, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233599, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233646, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233698, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233700, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233767, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755233770, "dur": 2015, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755235795, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755235799, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755235951, "dur": 10, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755235963, "dur": 55, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236039, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236042, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236097, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236101, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236152, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236201, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236203, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236241, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236351, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236353, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236409, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236412, "dur": 78, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236496, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236548, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236595, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236598, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236665, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236668, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236714, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236757, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236759, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236915, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236977, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755236983, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237034, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237036, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237084, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237087, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237132, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237226, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237271, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237274, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237321, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237323, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237388, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237431, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237435, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237484, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237534, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237580, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237583, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237634, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237636, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237915, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755237921, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238007, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238011, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238048, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238052, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238090, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238092, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238141, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238144, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238194, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238197, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238285, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238288, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238332, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238335, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238392, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238394, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238529, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238577, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238579, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238620, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238622, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238821, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238870, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238913, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238916, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238970, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755238973, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239028, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239030, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239073, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239075, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239117, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239119, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239167, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239171, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239220, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239222, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239300, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239348, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239352, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239406, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239408, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239450, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239453, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239560, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239612, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239616, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239667, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239750, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239754, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239814, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755239818, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241570, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241578, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241715, "dur": 13, "ph": "X", "name": "ProcessMessages 1040", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241730, "dur": 48, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241781, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241784, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241824, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241827, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241876, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241922, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241927, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241975, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755241977, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242026, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242063, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242065, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242110, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242112, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242154, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242156, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242237, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242240, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242297, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242300, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242352, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242354, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242426, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242429, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242486, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242547, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242551, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242601, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242604, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242664, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242666, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242710, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242712, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242753, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242757, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242800, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242803, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242852, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242855, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242913, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242918, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242963, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755242966, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755243015, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755243018, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755243077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755243079, "dur": 38215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281303, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281311, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281383, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281639, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281642, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281673, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755281675, "dur": 2222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755283905, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755283910, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755283990, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755283995, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284039, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284041, "dur": 343, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284391, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284395, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284442, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755284445, "dur": 50861, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335314, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335319, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335379, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335382, "dur": 487, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335877, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335881, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335959, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755335963, "dur": 261, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755336230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755336233, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755336302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755336305, "dur": 6365, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755342680, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755342684, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755342747, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755342750, "dur": 558, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343317, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343321, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343386, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343389, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343575, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343578, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343628, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755343631, "dur": 713, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755344354, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755344358, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755344446, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755344450, "dur": 1047, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345510, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345565, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345568, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345675, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345728, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345731, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345919, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345922, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345979, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755345981, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346026, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346029, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346205, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346248, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346251, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346282, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346470, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755346519, "dur": 870, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347398, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347402, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347461, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347464, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347513, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347515, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347556, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347627, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347672, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347675, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347738, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347803, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347805, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347864, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347906, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755347910, "dur": 1236, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349156, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349161, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349227, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349229, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349338, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349343, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349412, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349415, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349726, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349730, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349759, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349761, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349819, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349823, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349893, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755349951, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350034, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350036, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350082, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350185, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350230, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350290, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350293, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350448, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350552, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350556, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350608, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350731, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350774, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755350776, "dur": 1767, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352555, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352560, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352637, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352640, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352681, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352684, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352981, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755352985, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353051, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353053, "dur": 473, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353534, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353538, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353610, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353777, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353780, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353829, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353832, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755353976, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354020, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354022, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354197, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354239, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354241, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354450, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354500, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755354503, "dur": 534, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355060, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355063, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355123, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355126, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355190, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355222, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355263, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355265, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355390, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355435, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355438, "dur": 118, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355561, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355565, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355613, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355616, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355655, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355688, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355805, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355807, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355846, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355918, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755355957, "dur": 620, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356585, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356589, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356645, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356648, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356736, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356796, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356801, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356855, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356857, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356891, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356893, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755356995, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357043, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357045, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357105, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357107, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357153, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357158, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357353, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357399, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357402, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357446, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357448, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357581, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357621, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357624, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357789, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357833, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755357835, "dur": 203, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358051, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358108, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358110, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358190, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358238, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755358240, "dur": 756, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359005, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359009, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359076, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359079, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359118, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359298, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359347, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359350, "dur": 472, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359834, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359897, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755359901, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360251, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360255, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360321, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360324, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360607, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360611, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360645, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755360646, "dur": 481, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361137, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361141, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361200, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361203, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361258, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361297, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361424, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361428, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361481, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361483, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361584, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361628, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361899, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755361903, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362004, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362006, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362053, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362055, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362337, "dur": 17, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362356, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362396, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362398, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362434, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362436, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362481, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362483, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362550, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362597, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362599, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362648, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362651, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362904, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362957, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755362960, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363259, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363328, "dur": 444, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363779, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363783, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363899, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363901, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755363999, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364067, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364069, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364194, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364251, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364253, "dur": 663, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364924, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364929, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364984, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755364986, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365146, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365150, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365179, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365334, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365371, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365374, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365407, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365409, "dur": 293, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365709, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365713, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365804, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755365807, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755366166, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755366172, "dur": 3425, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755369607, "dur": 1160, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755370875, "dur": 3705, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374591, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374595, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374656, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374660, "dur": 78, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374744, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374786, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755374788, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375432, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375435, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375497, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375772, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375818, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375821, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375897, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755375955, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376010, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376059, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376061, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376192, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376228, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376230, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376346, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376437, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376440, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376497, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376583, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376619, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376622, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376717, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376758, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755376760, "dur": 450, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377219, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377223, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377282, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377333, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377369, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377618, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377670, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377672, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377819, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377878, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377883, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377918, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377920, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755377962, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378026, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378064, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378066, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378200, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378236, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378238, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378282, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378286, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378441, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378502, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378505, "dur": 407, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378919, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378924, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378972, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755378975, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755379163, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755379206, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755379208, "dur": 1592, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380810, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380815, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380889, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380893, "dur": 38, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380935, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755380937, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381055, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381097, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381223, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381277, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381279, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381326, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381329, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381372, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381375, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381527, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381529, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381568, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381570, "dur": 305, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381881, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381885, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381927, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755381929, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382214, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382263, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382267, "dur": 483, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382758, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382763, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382829, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382833, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382947, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382989, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755382991, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383214, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383257, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383296, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383298, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383604, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383609, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383668, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383671, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383718, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383720, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383870, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755383911, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384076, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384109, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384114, "dur": 696, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384816, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384877, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755384880, "dur": 1350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386238, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386242, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386290, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386292, "dur": 120, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386418, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372755386454, "dur": 898693, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756285155, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756285159, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756285191, "dur": 2650, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756287847, "dur": 15233, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756303089, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756303093, "dur": 5822, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756308930, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756308938, "dur": 302, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756309244, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756309249, "dur": 2612, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756311869, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756311874, "dur": 143, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756312022, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756312025, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756312084, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756312087, "dur": 12536, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324652, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324658, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324766, "dur": 13, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324781, "dur": 40, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324825, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324827, "dur": 106, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324937, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324940, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324992, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756324994, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325053, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325055, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325087, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325090, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325207, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325210, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325253, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325256, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325313, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325316, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325337, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325493, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325532, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325536, "dur": 13, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325553, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325675, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325678, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325842, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325847, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325875, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325877, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325929, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325931, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325980, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756325982, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326124, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326128, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326213, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326218, "dur": 59, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326282, "dur": 15, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326301, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326357, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326360, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326398, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326461, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326463, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326511, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326514, "dur": 150, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326670, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326673, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326735, "dur": 4, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326740, "dur": 43, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326788, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326831, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326833, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326883, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326934, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756326937, "dur": 413, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756327356, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756327392, "dur": 342, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 9748, "tid": 12884901888, "ts": 1754372756327738, "dur": 12819, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756370438, "dur": 5789, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 9748, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 9748, "tid": 8589934592, "ts": 1754372755119571, "dur": 282839, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 9748, "tid": 8589934592, "ts": 1754372755402413, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 9748, "tid": 8589934592, "ts": 1754372755402419, "dur": 5240, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756376230, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 9748, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 9748, "tid": 4294967296, "ts": 1754372755067167, "dur": 1276214, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 9748, "tid": 4294967296, "ts": 1754372755079315, "dur": 29458, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 9748, "tid": 4294967296, "ts": 1754372756343802, "dur": 11719, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 9748, "tid": 4294967296, "ts": 1754372756349036, "dur": 4606, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 9748, "tid": 4294967296, "ts": 1754372756355620, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756376242, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754372755151681, "dur": 66, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372755151781, "dur": 37338, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372755189139, "dur": 1632, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372755190971, "dur": 96, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754372755191067, "dur": 465, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372755191679, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_D8631827C2346E7C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754372755192759, "dur": 220, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_6823D15ACBA75F2D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754372755195247, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755196277, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754372755199378, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754372755202682, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754372755204391, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754372755204632, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754372755205715, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755208394, "dur": 881, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Profiling.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755209639, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755211377, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755212344, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755212716, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755213971, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755216444, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755219023, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755220499, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754372755222089, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754372755191576, "dur": 31811, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372755223416, "dur": 1105167, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372756328584, "dur": 162, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372756328875, "dur": 84, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372756328991, "dur": 1704, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754372755191934, "dur": 31497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755223476, "dur": 677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755224194, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_03EE02FA5C4C261D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755224593, "dur": 1036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755225635, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8B23194D4E1D78D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755225702, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755226212, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F61EBAADF392D364.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755226286, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755226665, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_B8F0F1673FF074D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755226770, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755227129, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_AEB2584A3223AB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755227255, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755227544, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_464CD038B10F6CF2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755227628, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755228162, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_F7DF7D9296BBB876.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755228256, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755228622, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_F7DF7D9296BBB876.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755228688, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_79FC607E907D8A09.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755228853, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_AA59D54E0A39A386.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755228953, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755229528, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_9B32DC7451EC00B8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755229762, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755230067, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_D097100DCF108B84.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755230133, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755230433, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_1972110A30DBB9C2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755230513, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755230579, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_AAF6DC82086A33E9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755230680, "dur": 607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755231358, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C3276692DFDC3F55.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755231472, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755231761, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755232254, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755232649, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755232829, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755233348, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755233580, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755233906, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755234119, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755234576, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755234855, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755235025, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755235254, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755235647, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755236098, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755236358, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755236908, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755237023, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755237519, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755237777, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754372755237910, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755238469, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755238937, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755239368, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755239628, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755239751, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755240222, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755240614, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755240843, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755241059, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755241437, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755241656, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755241938, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755242275, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755242390, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755242509, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755242980, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755243754, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12436201776501168220.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754372755243900, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755244247, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755244605, "dur": 2360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755248787, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Assets\\Tools\\Runtime\\Animations\\ITransformPresetHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755246965, "dur": 3058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755250023, "dur": 3701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755253726, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\CurvesOwner\\ICurvesOwnerInspectorWrapper.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755253726, "dur": 3986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755257713, "dur": 3369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755261083, "dur": 3191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755264275, "dur": 1961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755266237, "dur": 2507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755268745, "dur": 3496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755273238, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\2D\\ShapeEditor\\EditablePath\\IEditablePath.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755272242, "dur": 4184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755277145, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Enumerations\\StructFieldOptions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755276427, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755278207, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755279971, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Controls\\TextureControl.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755280844, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Controls\\TextControl.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755279256, "dur": 3752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755284695, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Math\\Trigonometry\\HyperbolicCosineNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755283009, "dur": 2498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755285511, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755285621, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755288499, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Graphs\\ShaderDropdown.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755287871, "dur": 2800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755292222, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstAotCompiler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755290672, "dur": 3881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755294553, "dur": 3521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755298074, "dur": 3063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755301138, "dur": 2946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755304085, "dur": 3227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755307683, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Editor\\GUI\\AssetSettingsAnalyzeTreeView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755307313, "dur": 3342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755311557, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@77ab6c14086a\\Editor\\TMP\\PropertyDrawers\\GlyphMetricsPropertyDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755310656, "dur": 3710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755314367, "dur": 3164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755319261, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationQuit.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755317532, "dur": 3296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755320828, "dur": 2968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755326702, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Reflection\\Codebase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755323797, "dur": 3488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755327285, "dur": 2985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755330762, "dur": 814, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Context\\GraphContextMenuItem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755330271, "dur": 3254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755336925, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\RenderGraph\\Compiler\\CompilerContextData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755333526, "dur": 3930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755337508, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755338660, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755340236, "dur": 3528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755345805, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Graphs\\GraphPointerException.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755343765, "dur": 3862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755347633, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755347863, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755348115, "dur": 6932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755355048, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755355159, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755355388, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755356761, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755357047, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755357216, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755357438, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755358009, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755358371, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755358610, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755359289, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755359728, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755360513, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755360954, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755361609, "dur": 1093, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755362725, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755363014, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755363092, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755363796, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755364262, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsTypeExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755364262, "dur": 3328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755367948, "dur": 933, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\ReflectedCloner.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755367591, "dur": 2480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755370072, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755371225, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\UI\\StandaloneInputModuleEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754372755371034, "dur": 2318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755373354, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755373598, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755373850, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755375104, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755375716, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755375941, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755377674, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755377847, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755378126, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755379367, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755379448, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755381113, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755381353, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755381536, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755381606, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755382405, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755382693, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755383522, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755383739, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755383802, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755384709, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755384898, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754372755385100, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755385227, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754372755386226, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372755386449, "dur": 914284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756300734, "dur": 3297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756304032, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756304676, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756307462, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756307860, "dur": 2786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756310647, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756311104, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756313551, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756313751, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756316401, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756316489, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756319314, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756319525, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756322098, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756322322, "dur": 2678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754372756325001, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756325305, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756325585, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756325754, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756326132, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756326531, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756326717, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756326896, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756327195, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756327374, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756327744, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756327949, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756328231, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754372756328421, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755192032, "dur": 31432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755223476, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_267CE70C43F79026.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755224576, "dur": 834, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755225417, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F0A81BCB4B15BC2E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755225487, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755225920, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_1F37E0994315CA47.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755225977, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755226516, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0706EDE95CDBA86D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755226623, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755226878, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_1065361CC9511C52.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755227008, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_1F3CCB04AD413EE4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755227091, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755227148, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_EA8955CF84CC09A2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755227217, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755227402, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F043E0D08E67957D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755227484, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755227858, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9CCBC234D52E0E8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755227971, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755228619, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9CCBC234D52E0E8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755228675, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_416E4096F05634D7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755228765, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755229250, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_24560D99108D46A6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755229390, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755229677, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_24AD519CD7E55433.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755229760, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755230213, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_3B8DCEEF2500563E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755230325, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755230573, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_517B04230F3E13FE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755230651, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755230962, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_7AEF20BDB113CC4D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755231116, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_A0DD4FB19A438019.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755231188, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755231553, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755231742, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755232060, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755232269, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755232642, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755232962, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755233399, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755233670, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755234035, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755234424, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755234752, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755234931, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755235168, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755235539, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755235858, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754372755235922, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755236292, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755236566, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755236895, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755237297, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755237668, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755238016, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755238373, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755238793, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755239277, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755239779, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755240008, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755240453, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755240811, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755241018, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755241245, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755241499, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755241815, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755242076, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755242672, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755242915, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754372755243119, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755243445, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755243895, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755245386, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Tests\\Editor\\DocExampleCode\\LoadWithReference.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755244614, "dur": 3366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755247981, "dur": 3390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755253793, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Recording\\TrackAssetRecordingExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755251372, "dur": 3977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755255350, "dur": 2744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755258095, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755260087, "dur": 1924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755262012, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755264282, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755266675, "dur": 2462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755269138, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755273203, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\2D\\ShapeEditor\\GUIFramework\\Control.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755271817, "dur": 3450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755277150, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Targets\\Canvas\\CanvasProperties.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755275267, "dur": 2454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755277722, "dur": 2756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755280917, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\DefaultShaderIncludes.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755280479, "dur": 3393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755284744, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Input\\Texture\\Texture3DAssetNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755283873, "dur": 2721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755288487, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Graphs\\Texture3DMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755286595, "dur": 3289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755289884, "dur": 2266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755292335, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Runtime\\ShadowCulling.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755292151, "dur": 3397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755295550, "dur": 3596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755299403, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Editor\\ICoreRenderPipelinePreferencesProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755299147, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755303054, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Ports\\UnitPortDefinitionInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755300593, "dur": 3151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755303745, "dur": 3525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755307271, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755308946, "dur": 2373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755311502, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Ports\\InvalidOutput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755311320, "dur": 3577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755315996, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Generic\\GenericSum.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755314897, "dur": 2426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755319331, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755317324, "dur": 3653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755320978, "dur": 2920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755326807, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugins\\PluginConfigurationItemAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755323898, "dur": 4053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755330819, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\IndividualPropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755327952, "dur": 3736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755331688, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755336896, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeBakingSet.Editor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755333756, "dur": 3721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755337477, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755338788, "dur": 2420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755341210, "dur": 2389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755343600, "dur": 5111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755348716, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755349014, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755349251, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755349504, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755350866, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755351348, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755351420, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755351670, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755351991, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755352178, "dur": 5759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755357939, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755358182, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755358445, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755358629, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755358990, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755359270, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755359429, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755359666, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755360450, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755360732, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755361800, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755362215, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755362531, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755362884, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755362975, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755363757, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755364075, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755364236, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorLabelAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755364235, "dur": 3457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755367908, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755371152, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\ExceptionHelpers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755368658, "dur": 3469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755372128, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755375503, "dur": 853, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@946053a44d19\\Editor\\Views\\PendingChanges\\PendingChangesTreeHeaderState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754372755374038, "dur": 2665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755377305, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755377686, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755377990, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755378771, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755379838, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755379912, "dur": 1447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755381361, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754372755381587, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755381654, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754372755382802, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755382943, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755383168, "dur": 33266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372755416435, "dur": 884287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756300726, "dur": 3293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756304020, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756304672, "dur": 2764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756307437, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756307686, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756310457, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756310676, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756313364, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756313633, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756316341, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756316612, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756319333, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756319397, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756322052, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756322158, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756324771, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756324843, "dur": 2645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754372756327490, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756327886, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754372756328323, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755192005, "dur": 31447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755223476, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755224179, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_79FB64A6FBBA2783.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755224616, "dur": 863, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755225487, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_9523758EF208ECDA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755225547, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755226052, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_78478CF207FCCAE5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755226113, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755226600, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_7B9D8E05B357E598.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755226756, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755227255, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_7B5D73E782A79508.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755227360, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755227571, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_31E746D22C84B46E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755227678, "dur": 928, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755228643, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_4413BC7439DF3EE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755228725, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755229253, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_84206FBA0FB43B9B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755229666, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755229929, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_58781EAD9BED3F82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755230319, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_6C46F97A134766DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755230468, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_6C46F97A134766DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755230527, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D18C74AAA9218131.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755230815, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D18C74AAA9218131.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755230930, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_905AD46631ED908C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755231054, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755231164, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_610808C823B7BA3A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755231239, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755231341, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5D0DB221CD49F42D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755231452, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755231682, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755231735, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755231933, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_C8961687FB20E552.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755232019, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755232367, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755232449, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755232540, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755232817, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755232886, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755233214, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755233289, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755233501, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755233818, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755234196, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755234428, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755234690, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755234898, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755235227, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755235604, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755236016, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755236225, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755236448, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755236760, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755236976, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755237722, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755238239, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755238352, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755238592, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755238942, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755239450, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755239801, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755239912, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754372755239977, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755240627, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755240851, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755241250, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755241844, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755242138, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755242365, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755242612, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755242965, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755243174, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755243381, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755243517, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755244033, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755244593, "dur": 3664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755248258, "dur": 3401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755253621, "dur": 821, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\TimelineClipGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755251660, "dur": 3476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755255137, "dur": 3252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755258391, "dur": 1987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755262431, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.Native.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754372755260379, "dur": 2912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755263542, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754372755263292, "dur": 2957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755266250, "dur": 2775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755269025, "dur": 3315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755272340, "dur": 3608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755277068, "dur": 654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Processors\\PropertyCollector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755275949, "dur": 2489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755279993, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Interfaces\\IResizable.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755278439, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755280789, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Util\\KeywordUtil.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755280789, "dur": 3406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755284665, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Input\\Scene\\SceneDepthNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755284196, "dur": 2289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755288438, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Graphs\\VertexColorMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755286486, "dur": 2560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755289047, "dur": 2813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755292175, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.searcher@1e17ce91558d\\Editor\\Searcher\\SearcherWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755291861, "dur": 2711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755296870, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Runtime\\LightCookieManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755294573, "dur": 3784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755298358, "dur": 2147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755300507, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755303072, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IsVariableDefinedUnitOption.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755302070, "dur": 3292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755307678, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\GPUDriven\\InstanceData\\InstanceAllocator.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755305363, "dur": 3790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755311504, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Editor\\Build\\BuildUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755309153, "dur": 3519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755312673, "dur": 2885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755315959, "dur": 746, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NotEqual.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755315559, "dur": 3585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755319309, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Control\\For.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755319145, "dur": 3826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755322972, "dur": 2939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755325911, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755328625, "dur": 2989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755331615, "dur": 2311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755333927, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755337260, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755337474, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755339544, "dur": 3210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755344863, "dur": 763, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionExitMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755345627, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionExit2DMessageListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755342755, "dur": 3922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755346734, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755347021, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755347311, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755348281, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755348988, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755349208, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755349531, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755350422, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755350724, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755350972, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755351223, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755351513, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755351812, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755352057, "dur": 11099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755363157, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755363523, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755363595, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755363876, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755363957, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755365109, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755365396, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755365633, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755366241, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755366550, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755366788, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755368026, "dur": 721, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\GUIStyle_DirectConverter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755367014, "dur": 2543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755371165, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\XR\\Haptics\\BufferedRumble.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755369558, "dur": 2642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755374036, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Events\\InputEventListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754372755372201, "dur": 2361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755374563, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755375779, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755376253, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755376896, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755377113, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755377330, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755377741, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755377946, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755378223, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755378645, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755378977, "dur": 1868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755380846, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755381074, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755381276, "dur": 2301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755383579, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754372755383820, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754372755384538, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372755385325, "dur": 915395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756300722, "dur": 3269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756303992, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756304710, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756307676, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756307911, "dur": 2705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756310617, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756310936, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756313572, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756313839, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756316705, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756316846, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756319596, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756320384, "dur": 2877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756323263, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756323424, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754372756326023, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756326248, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756326423, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756326631, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756326807, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756326866, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756327046, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756327418, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756327815, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754372756328026, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756328192, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756328256, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754372756328332, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755192099, "dur": 31380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755223489, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_08ABB9D1686EB77E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755224538, "dur": 712, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755225291, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_BC1533A8C8D75703.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755225413, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755225848, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_49F09E84C1CA16F0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755225911, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755226223, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_BFAC5AB404D3B45A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755226312, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755226958, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_5C38ABF0EFC33D99.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755227048, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755227252, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_13864A3621E56943.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755227396, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_688E581695FE3DD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755227460, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755227801, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_3005DC23F2D02D13.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755227884, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755228529, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_1FCBCB3D6D4DF0BD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755228632, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755228866, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_8C075F1C78C65131.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755228957, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755229353, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_CA4A9C887A23A089.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755229459, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755229649, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_9F8FC40C8A4E3CCF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755229752, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755230030, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_035C13B99BF224FB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755230163, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_7084FEA09D83CAA4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755230239, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755230430, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_E0CD2FA7B446BAF2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755230562, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5BA6911B27E787DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755230638, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755231052, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_16268653A2FF3EE8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755231263, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755231656, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755231868, "dur": 53274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755285143, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755285564, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755285808, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755285994, "dur": 50684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755336680, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755336965, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755337567, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755337835, "dur": 5482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755343318, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755343671, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755343889, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755344092, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755344345, "dur": 10629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755354976, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755355427, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755355618, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755355870, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755356076, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755356659, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755357021, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755357338, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755357560, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755358175, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755358441, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755358715, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755358993, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755359220, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755359418, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755359566, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755359823, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755360452, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755360605, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755361131, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755361406, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755362526, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755362908, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755363114, "dur": 3052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755366182, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755366466, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755366538, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755367198, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755368025, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Collections\\NonNullableDictionary.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754372755371130, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Collections\\AotDictionary.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754372755367340, "dur": 4853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755372193, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755374064, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755374999, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755375955, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755376311, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755376403, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755377165, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755377414, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755377706, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755378023, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755378351, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755379038, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755379259, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755379536, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755380614, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755380800, "dur": 1822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755382624, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755382833, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755382988, "dur": 1983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755384972, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755385256, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755385498, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755385721, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755386487, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755386890, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755387097, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754372755387272, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754372755387777, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372755388057, "dur": 912654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756300713, "dur": 3154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756303868, "dur": 785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756304668, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756307479, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756307736, "dur": 2690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756310428, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756310673, "dur": 2835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756313509, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756313609, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756316380, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756316593, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756319229, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756319343, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756322032, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756322257, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756324899, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756324967, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754372756327489, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756327734, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756327912, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756328081, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754372756328311, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755192169, "dur": 31324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755223507, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_08163148C33DB7C3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755224627, "dur": 917, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755225550, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_30D4DF2B223FF2C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755225614, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755225873, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_47F862A16E766A21.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755225938, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755226225, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CA308FCF3F65CFDF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755226316, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755227017, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_672501763CC4F802.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755227126, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755227343, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_7966E32B734E51DC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755227420, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755227639, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_2F90F0B9089114A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755227739, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755227927, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_62C49F287C0A7A71.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755228004, "dur": 848, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755228860, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D9554151338ECF01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755228949, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755229434, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_3ACC75A2EDA43B81.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755229706, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8B95AE97B2023317.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755229773, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755230215, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_710797B72EBADE1E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755230309, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755230501, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_710797B72EBADE1E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755230568, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0CFF6EC83F72E03C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755230673, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755231146, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BCE6E4F37BFF1616.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755231402, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_70C0C2C8ACBBB483.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755231466, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755231805, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755231986, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755232142, "dur": 50673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754372755282817, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755282926, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755284605, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Math\\Range\\OneMinusNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755283297, "dur": 2627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755285925, "dur": 2770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755288695, "dur": 3368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755292245, "dur": 839, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Runtime\\UniversalRendererRenderGraph.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755292064, "dur": 4328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755296393, "dur": 2302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755299603, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Editor\\Lighting\\ProbeVolume\\ProbeGIBaking.Invalidation.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755298696, "dur": 3557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755303107, "dur": 856, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphInputWidget.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755302254, "dur": 4063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755307603, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\GPUDriven\\Debug\\DebugDisplayGPUResidentDrawer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755306319, "dur": 3279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755311571, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Editor\\AddressableAssetSettingsDefaultObject.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755309599, "dur": 3477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755313077, "dur": 2913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755315990, "dur": 698, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\TriggerEventUnit.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755319267, "dur": 866, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\OnEnable.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755315990, "dur": 4674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755320664, "dur": 3518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755326786, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_4_7.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755324182, "dur": 3476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755327659, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755330868, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Description\\MachineDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755329872, "dur": 4003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755333876, "dur": 3847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755337724, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755339324, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.scriptablebuildpipeline@46e2441c9992\\Editor\\Interfaces\\IBuildParameters.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754372755338542, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755340523, "dur": 3483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755344009, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755344280, "dur": 12259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754372755356540, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755356669, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755356898, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755357110, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755357193, "dur": 6613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754372755363808, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755364133, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755364534, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754372755364687, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755364886, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754372755365399, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755365855, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1754372755366958, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372755367764, "dur": 919017, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1754372756300709, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756303609, "dur": 1033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756304678, "dur": 2789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756307469, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756307683, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756310377, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756310493, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756313190, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756313881, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756316679, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756316766, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756319522, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756319587, "dur": 3898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756323487, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756323755, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754372756326468, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756326542, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756326753, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756327024, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756327220, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756327655, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756327748, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756327897, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756328079, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754372756328342, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755192240, "dur": 31272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755223523, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FFEDC37B7293E08F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755224592, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755225280, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_FB3B0C947E619491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755225423, "dur": 713, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755226142, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_877265938EA13316.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755226270, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755226669, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_EF6162F33D2BF2BF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755226754, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755227107, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_275F1885DA93487C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755227228, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9F81FDB5650BFFA6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755227298, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755227601, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_7FD92AC83D67875E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755227673, "dur": 794, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755228476, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_661C8B406434F8DC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755228659, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_9A6B10D363DE3A6C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755228746, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755228932, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_236F4E6598E106A0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755229007, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755229413, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_6823D15ACBA75F2D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755229498, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755229859, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_84B61E1AC8F1043A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755229995, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_D8631827C2346E7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755230124, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CD497CA3142053AA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755230211, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755230496, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_CD497CA3142053AA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755230552, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_27AD325C159FBEE0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755230665, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755231084, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1DEA7D726442350D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755231193, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755231564, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755231634, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755231879, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B02294E50AF32FE5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755232033, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754372755232084, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755232348, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755232668, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755233033, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755233563, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755233868, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755234054, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755234496, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755234674, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755234866, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755235045, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754372755235103, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755235508, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754372755235563, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755235924, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755236144, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755236453, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755236893, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755237225, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755237551, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755237861, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755237966, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755238203, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755238593, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755239104, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755239204, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755239720, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755239965, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755240190, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755240727, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755240937, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755241147, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755241394, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755241669, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755241984, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755242269, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755242483, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755242904, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755243267, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755243841, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755244161, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755244376, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755244599, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755248733, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Assets\\Tools\\Runtime\\Patterns\\EventBus\\IEventListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755246505, "dur": 3575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755250081, "dur": 2672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755253816, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Items\\ClipItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755252755, "dur": 3879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755256634, "dur": 2910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755259545, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755261451, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755263541, "dur": 2484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755266026, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755268036, "dur": 3695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755273324, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\2D\\ShapeEditor\\Shapes\\Polygon.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755271732, "dur": 3657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755277043, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInCanvasSubTarget.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755275390, "dur": 3422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755279998, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Inspector\\PropertyDrawers\\DropdownPropertyDrawer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755280803, "dur": 807, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Inspector\\PropertyDrawers\\CubemapPropertyDrawer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755278813, "dur": 3381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755284778, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Utility\\Logic\\AllNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755282195, "dur": 3392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755285588, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755288448, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Graphs\\MatrixShaderProperty.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755288000, "dur": 2919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755292175, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.State\\StateGraphEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755290920, "dur": 3215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755294136, "dur": 3939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755299397, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Editor\\MaterialUpgrader.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755298075, "dur": 1983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755300058, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755303083, "dur": 808, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_1_1_to_1_1_2.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755300792, "dur": 3670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755304463, "dur": 3483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755307947, "dur": 2849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755311440, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Ports\\UnitPortDefinition.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755310797, "dur": 3342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755316025, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Average.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755314140, "dur": 3171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755319256, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\IMouseEventUnit.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755317312, "dur": 3912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755321225, "dur": 3226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755326820, "dur": 909, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_6.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755324452, "dur": 3315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755327767, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755330697, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\BoltStyles.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755330697, "dur": 3509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755336890, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\Debugging\\MousePositionDebug.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754372755334207, "dur": 3888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755338096, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755339058, "dur": 3138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755342197, "dur": 3426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755345628, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755345866, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755345942, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755346691, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755347111, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755347370, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755347630, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755348740, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755349115, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755349372, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755351134, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755351808, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755351871, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755352057, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755352357, "dur": 1589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755353948, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755354157, "dur": 22468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755376626, "dur": 900, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755377544, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755377831, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755378151, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755378244, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755379338, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755379544, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754372755379793, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755379909, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754372755380737, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755381434, "dur": 27861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755413986, "dur": 469, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1754372755414456, "dur": 1784, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1754372755416241, "dur": 178, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1754372755409297, "dur": 7130, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372755416428, "dur": 884279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756300726, "dur": 3609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756304337, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756304761, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756307559, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756307969, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756310790, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756311116, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756313898, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756314117, "dur": 2974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756317093, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756317209, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756319939, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756320020, "dur": 5389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754372756325411, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756325776, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326043, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326151, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326318, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326489, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326662, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756326835, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756327065, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756327536, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756327806, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754372756327867, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756327991, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756328238, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754372756328455, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755192292, "dur": 31238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755223543, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_0D6792B2359E88E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755224555, "dur": 1119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755225682, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_E437F8F60EEF9C25.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755225754, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755226232, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_FE1FA67B235BC887.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755226303, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755226829, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_CC0C773C887EF0CD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755226931, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755227191, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_DD33B59E5115A79B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755227253, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755227468, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_02472375560A56AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755227578, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755227764, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_8C5B6E0524570744.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755227836, "dur": 886, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755228730, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E9DAE8878287F057.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755228808, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755229369, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_38A61D4D58D0E4BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755229470, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755229823, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_0BA3D4322A4E23F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755229983, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_90CD51A653E26C17.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755230129, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2DDB9365105169C6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755230215, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755230558, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_BEFDED6C028F9B0F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755230650, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755230968, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_B32B21D82DB66454.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755231061, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755231120, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_E8C5862329C73937.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755231206, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755231469, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755231700, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755231949, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755232255, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755232680, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755232872, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755233142, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755233364, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755233422, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755233756, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755233924, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755234066, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755234580, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754372755234635, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755234881, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755235086, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755235366, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755235768, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755236213, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755236418, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755236654, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755237059, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755237433, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755237598, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755238007, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754372755238122, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755238363, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755238683, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754372755238762, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755239274, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755239733, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755240017, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755240779, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755240966, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755241402, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755241587, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755241958, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755242273, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755242684, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755243450, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755243891, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755244148, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755245326, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Tests\\Editor\\DocExampleCode\\ScriptReference\\UsingLoadResourceLocations.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755244602, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755246670, "dur": 2925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755249596, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755253753, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Move\\MoveItemModeReplace.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755252309, "dur": 4217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755256527, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755259143, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755261196, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755263486, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755265425, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755266904, "dur": 2772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755270358, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\Converter\\ConversionIndexers.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755273167, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Drawers.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755269677, "dur": 4286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755273963, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755276660, "dur": 2084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755279932, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Inspector\\PropertyDrawers\\Vector2PropertyDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755280817, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ToggleDataPropertyDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755278745, "dur": 3677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755284604, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\NodeClassCache.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755282422, "dur": 3243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755285666, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755288415, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Graphs\\GradientMaterialSlot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755288133, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755290721, "dur": 3216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755293938, "dur": 3545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755299555, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Editor\\SpeedTree8MaterialUpgrader.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755297484, "dur": 3051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755303246, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.State\\NesterState.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755300536, "dur": 4052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755304589, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755307612, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Editor\\Settings\\CcdFolder.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755306798, "dur": 2839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755309637, "dur": 3526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755313164, "dur": 2739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755315904, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\HasScriptGraph.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755319263, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnCollisionStay2D.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755315904, "dur": 4651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755320556, "dur": 2553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755323110, "dur": 2481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755325592, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755330714, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\GuidInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755327935, "dur": 4147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755332083, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755333138, "dur": 2572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755337017, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\Common\\ObservableList.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754372755335710, "dur": 3242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755338953, "dur": 2553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755341507, "dur": 3105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755344615, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755344819, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755344920, "dur": 30165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754372755375087, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755375237, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755375670, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755375935, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754372755377318, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755377430, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755377692, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755378210, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755378825, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754372755379937, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755380077, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755380537, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755380762, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755380993, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754372755382086, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755382201, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755382592, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754372755382831, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755382915, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754372755383693, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755383860, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372755384246, "dur": 916469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756300717, "dur": 2991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756303709, "dur": 1005, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756304720, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756307415, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756307787, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756310380, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756310644, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756313401, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756313653, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756316388, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756316606, "dur": 2628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756319235, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756319371, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756321995, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756322100, "dur": 2793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754372756324894, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756324997, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756325206, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756325545, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756325900, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756326133, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756326411, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756326605, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756326755, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756326931, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756327003, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756327243, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756327556, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756327773, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756327945, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754372756328240, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755192360, "dur": 31189, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755223550, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_92711B7591E8501E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755224661, "dur": 753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755225424, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5D0CF1039E16E814.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755225505, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755226026, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_5BBE031DD680430C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755226098, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755226524, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_74B95AA0C243B77E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755226636, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755226843, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E64AF467819FCDAB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755226942, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755227113, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_AF619D9DE10DFD5B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755227194, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755227382, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6DE916D45DB82D6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755227457, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755227706, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_49B6D6079A776FD8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755227801, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755228470, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_21649CF481B1EC63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755228576, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755228981, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_838A19A368FA3F33.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755229069, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755229318, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E1C01770F4CEA1EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755229391, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755229916, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C0CFA69685FFE00F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755230033, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_7F095BA95935F6B6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755230130, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755230366, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_DA4FF68F52754873.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755230489, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_DA4FF68F52754873.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755230544, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03A402038E83EC1F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755230632, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755230937, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F51454188AA47C53.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755231125, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_9B1E265E619AC557.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755231253, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755231354, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_70C0F7E9F94F5DC5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755231429, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755231658, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755231875, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755232120, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755232426, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755232782, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755233234, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_86E1581DC3169049.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755233575, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755233927, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754372755234111, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755234670, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755235072, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755235299, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755235704, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755236201, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755236512, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755236681, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755237151, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755237599, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755237959, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755238285, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755238637, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755239101, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755239244, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755239545, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755239916, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754372755239975, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755240542, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755240831, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755241038, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755241484, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755241673, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755241886, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755242637, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755242909, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755243183, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755243460, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755243546, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755243871, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755244408, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755244681, "dur": 2839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755248735, "dur": 753, "ph": "X", "name": "File", "args": {"detail": "Assets\\RTLTMPro\\Tests\\GlyphFixerTests.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755247521, "dur": 3684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755251206, "dur": 3466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755254673, "dur": 3227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755257901, "dur": 2514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755260420, "dur": 2784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755263205, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755265280, "dur": 2956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755268237, "dur": 3097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755273240, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\AssetPostProcessors\\ShaderGraphMaterialsUpdater.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755271335, "dur": 3663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755274999, "dur": 2781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755277781, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755280046, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Controls\\ColorControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755280935, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Drawing\\Controls\\ChannelEnumControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755280046, "dur": 3764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755284609, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Nodes\\Math\\Basic\\SquareRootNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755283811, "dur": 2538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755286350, "dur": 2047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755288593, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Data\\Attributes\\SubTargetFilterAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755288398, "dur": 2888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755292182, "dur": 654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.State\\Plugin\\BoltStateConfiguration.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755291287, "dur": 3728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755295016, "dur": 3626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755299656, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeLightingTab.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755298644, "dur": 3210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755303073, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Options\\UnitOptionFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755301861, "dur": 3423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755307609, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@e1ccf0da7b78\\Runtime\\GPUDriven\\RenderersParameters.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755305285, "dur": 3151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755308436, "dur": 2123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755311579, "dur": 841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@77ab6c14086a\\Editor\\TMP\\TMP_BaseShaderGUI.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755310560, "dur": 4227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755314788, "dur": 4150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755319332, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Control\\ToggleFlow.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755318939, "dur": 3442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755322382, "dur": 2922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755325305, "dur": 1924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755327230, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755329996, "dur": 3250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755333247, "dur": 2403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755335651, "dur": 3494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755339146, "dur": 2732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755341879, "dur": 2843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755344725, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755344891, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755345174, "dur": 2006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755347182, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755347314, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755347529, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755347722, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755347889, "dur": 6218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755354108, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755354265, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755354594, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755355309, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755355614, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755355829, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755356079, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755356705, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755356823, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755357032, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755357212, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755357294, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755358097, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755358363, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755358524, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755358730, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755358922, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755358973, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755359044, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755359398, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755359685, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755360433, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755360587, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755361124, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755361806, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755362791, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755362942, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755363212, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.NullableValueTypes.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755364144, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Comparables.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755363212, "dur": 3877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755367987, "dur": 982, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsDateConverter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755371175, "dur": 928, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Collections\\WatchedList.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754372755367089, "dur": 5336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755372426, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755374189, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755375952, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755376173, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755376261, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755377028, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755377399, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755377725, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755377930, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755377993, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755379159, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755379688, "dur": 1037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755380727, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755380949, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755381014, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755381079, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755382069, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755382208, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755382847, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754372755383082, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755383160, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754372755383990, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755384582, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372755384855, "dur": 915869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756300726, "dur": 3316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756304043, "dur": 607, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756304685, "dur": 2780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756307466, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756307708, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756310536, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756310619, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756313189, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756313295, "dur": 2833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756316129, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756316206, "dur": 2795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756319002, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756319263, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756321910, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756322225, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754372756325046, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756325305, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756325550, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756325760, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756326109, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756326372, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Tests.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754372756326433, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756326599, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756326851, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756327083, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756327350, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756327814, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754372756327887, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754372756328336, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754372756338700, "dur": 2989, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 9748, "tid": 17, "ts": 1754372756377012, "dur": 3187, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 9748, "tid": 17, "ts": 1754372756380263, "dur": 3521, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 9748, "tid": 17, "ts": 1754372756365064, "dur": 20275, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}