Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14242 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-04T18:05:23Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Development/Unity/UnityProjects/Packages
-logFile
Logs/AssetImportWorker0.log
-srvPort
3749
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Development/Unity/UnityProjects/Packages
D:/Development/Unity/UnityProjects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [15704]  Target information:

Player connection [15704]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3879034712 [EditorId] 3879034712 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15704]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3879034712 [EditorId] 3879034712 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15704]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3879034712 [EditorId] 3879034712 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15704]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3879034712 [EditorId] 3879034712 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15704]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3879034712 [EditorId] 3879034712 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [15704] Host joined multi-casting on [***********:54997]...
Player connection [15704] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 202.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Development/Unity/UnityProjects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon(TM) RX Vega 11 Graphics (ID=0x15d8)
    Vendor:          ATI
    VRAM:            7121 MB
    App VRAM Budget: 8439 MB
    Driver:          31.0.21921.1000
    Unified Memory Architecture
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56800
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.014067 seconds.
- Loaded All Assemblies, in 62.677 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 829 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.933 seconds
Domain Reload Profiling: 65591ms
	BeginReloadAssembly (53642ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (8ms)
	RebuildCommonClasses (1561ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (157ms)
	LoadAllAssembliesAndSetupDomain (7272ms)
		LoadAssemblies (53685ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (7199ms)
			TypeCache.Refresh (7195ms)
				TypeCache.ScanAssembly (6205ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2776ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1326ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (897ms)
			ProcessInitializeOnLoadMethodAttributes (383ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 17.909 seconds
Refreshing native plugins compatible for Editor in 2.60 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.655 seconds
Domain Reload Profiling: 22551ms
	BeginReloadAssembly (508ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (93ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (110ms)
	LoadAllAssembliesAndSetupDomain (17155ms)
		LoadAssemblies (15148ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2283ms)
			TypeCache.Refresh (2111ms)
				TypeCache.ScanAssembly (1965ms)
			BuildScriptInfoCaches (140ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (4657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2894ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (348ms)
			ProcessInitializeOnLoadAttributes (2071ms)
			ProcessInitializeOnLoadMethodAttributes (414ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 4.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6643 unused Assets / (5.5 MB). Loaded Objects now: 7358.
Memory consumption went from 169.9 MB to 164.4 MB.
Total: 21.009600 ms (FindLiveObjects: 1.110600 ms CreateObjectMapping: 1.603500 ms MarkObjects: 12.632900 ms  DeleteObjects: 5.660600 ms)

========================================================================
Received Import Request.
  Time since last request: 87713.626075 seconds.
  path: Assets/Tools/Runtime/Monetization/Ads/Common/AdManager.cs
  artifactKey: Guid(df9f74868176a0442aee9105118f129d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Monetization/Ads/Common/AdManager.cs using Guid(df9f74868176a0442aee9105118f129d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '687b5e4bbb6ce554331c62e87b68d3c4') in 0.0144784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

