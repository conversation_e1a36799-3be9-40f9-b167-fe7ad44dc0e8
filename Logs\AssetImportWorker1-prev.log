Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14242 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-04T18:05:23Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Development/Unity/UnityProjects/Packages
-logFile
Logs/AssetImportWorker1.log
-srvPort
3749
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Development/Unity/UnityProjects/Packages
D:/Development/Unity/UnityProjects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12412]  Target information:

Player connection [12412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1465469886 [EditorId] 1465469886 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1465469886 [EditorId] 1465469886 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12412]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1465469886 [EditorId] 1465469886 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12412]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1465469886 [EditorId] 1465469886 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1465469886 [EditorId] 1465469886 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12412] Host joined multi-casting on [***********:54997]...
Player connection [12412] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 201.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Development/Unity/UnityProjects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon(TM) RX Vega 11 Graphics (ID=0x15d8)
    Vendor:          ATI
    VRAM:            7121 MB
    App VRAM Budget: 8439 MB
    Driver:          31.0.21921.1000
    Unified Memory Architecture
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56684
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.013013 seconds.
- Loaded All Assemblies, in 62.698 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 809 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.851 seconds
Domain Reload Profiling: 65542ms
	BeginReloadAssembly (53654ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (5ms)
	RebuildCommonClasses (1563ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (149ms)
	LoadAllAssembliesAndSetupDomain (7287ms)
		LoadAssemblies (53694ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (7203ms)
			TypeCache.Refresh (7200ms)
				TypeCache.ScanAssembly (6200ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2858ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2724ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1316ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (829ms)
			ProcessInitializeOnLoadMethodAttributes (413ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 17.907 seconds
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.645 seconds
Domain Reload Profiling: 22543ms
	BeginReloadAssembly (513ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (92ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (106ms)
	LoadAllAssembliesAndSetupDomain (17154ms)
		LoadAssemblies (15136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2288ms)
			TypeCache.Refresh (2105ms)
				TypeCache.ScanAssembly (1962ms)
			BuildScriptInfoCaches (144ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (4649ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2911ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (346ms)
			ProcessInitializeOnLoadAttributes (2078ms)
			ProcessInitializeOnLoadMethodAttributes (427ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 4.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6643 unused Assets / (5.1 MB). Loaded Objects now: 7358.
Memory consumption went from 170.0 MB to 164.9 MB.
Total: 20.830100 ms (FindLiveObjects: 1.174000 ms CreateObjectMapping: 1.597400 ms MarkObjects: 12.649500 ms  DeleteObjects: 5.407400 ms)

